from flask import Blueprint
from sqlalchemy import select, func, and_

from app.application.product.use_cases.products import GetPaginatedProductsUseCase, GetProductDetailUseCase
from app.application.product.use_cases.skus import GetSKUsByProductIDUseCase, GetSKUDetailByIDUseCase
from app.application.product.use_cases.update_product_publish_status_use_case import UpdateProductPublishStatusUseCase
from app.constants import <PERSON>rrorCode
from app.decorators.auth import admin_required
from app.decorators.validation import validate_with
from app.extensions import db
from app.infrastructure.product.repositories import SQLProductRepository
from app.interfaces.rest.schemas.product_schemas import ProductDetailQuerySchema, SKUDetailSchema, \
    SKUQueryByProductSchema, ProductTypeSchema, GetProductsByCategorySchema, GetProductsByGroupSchema, \
    GetProductsByChannelSchema, GetProductsByRegionSchema, UpdateProductPublishStatusSchema
from app.models import Category, ProductCategoryAssociation, ProductGroup, ProductGroupAssociation, Channel, \
    ChannelProductAssociation, Region, RegionProductAssociation
from app.models.product import Product, ProductTrace, ProductWiki, SKU
from app.schemas.home import PaginationSchema

bp = Blueprint('admin_product', __name__, url_prefix='/api/admin/products')


@bp.get('/base')
@admin_required
@validate_with(PaginationSchema)
def get_products(page, per_page):
    """管理员分页获取商品列表"""
    # 创建并执行用例
    use_case = GetPaginatedProductsUseCase(
        repo=SQLProductRepository()
    )

    # 返回统一格式响应
    return {
        'code': ErrorCode.SUCCESS.code,
        'data': use_case.execute(page, per_page)
    }


@bp.get('/detail')
@admin_required
@validate_with(ProductDetailQuerySchema)
def get_product_detail(product_id):
    """管理员获取商品详情，包含所有关联数据
    
    返回商品的所有关联数据，SKU列表仅包含简要信息（id、图片、名称）
    """
    # 创建并执行用例
    use_case = GetProductDetailUseCase(
        repo=SQLProductRepository()
    )

    result = use_case.execute(product_id)
    return {
        'code': ErrorCode.SUCCESS.code,
        'data': result
    }


@bp.get('/skus')
@admin_required
@validate_with(SKUQueryByProductSchema)
def get_product_skus(product_id):
    """管理员通过商品ID获取SKU列表
    
    返回包含sku的id、图片、名称等信息
    """
    # 创建并执行用例
    use_case = GetSKUsByProductIDUseCase(
        repo=SQLProductRepository()
    )

    result = use_case.execute(product_id)
    return {
        'code': ErrorCode.SUCCESS.code,
        'data': result
    }


@bp.get('/skus/detail')
@admin_required
@validate_with(SKUDetailSchema)
def get_sku_detail(sku_id):
    """管理员通过SKU ID获取SKU详情
    
    返回包含sku的所有相关信息
    """
    # 创建并执行用例
    use_case = GetSKUDetailByIDUseCase(
        repo=SQLProductRepository()
    )

    result = use_case.execute(sku_id)
    return {
        'code': ErrorCode.SUCCESS.code,
        'data': result
    }


@bp.get('/info-by-type')
@admin_required
@validate_with(ProductTypeSchema)
def get_products_info_by_type(type_):
    """管理员获取所有商品的特定类型信息列表
    
    返回所有商品的title、title_image，以及是否有百科/溯源和对应的图片数量
    """
    # 基础查询 - 获取所有未删除的商品
    base_query = select(
        Product.id,
        Product.title,
        Product.title_image,
        Product.is_published
    ).where(Product.deleted_at.is_(None))

    # 根据类型添加不同的连接查询
    if type_ == 'trace':
        # 使用exists子查询判断是否存在溯源记录
        stmt = base_query.add_columns(
            db.exists().where(
                ProductTrace.product_id == Product.id
            ).label('has_info'),
            db.func.coalesce(
                db.select(db.func.count(ProductTrace.images))
                .where(ProductTrace.product_id == Product.id)
                .scalar_subquery(),
                0
            ).label('info_count')
        )

    elif type_ == 'wiki':
        # 使用exists子查询判断是否存在百科记录
        stmt = base_query.add_columns(
            db.exists().where(
                ProductWiki.product_id == Product.id
            ).label('has_info'),
            db.func.coalesce(
                db.select(db.func.count(ProductWiki.images))
                .where(ProductWiki.product_id == Product.id)
                .scalar_subquery(),
                0
            ).label('info_count')
        )
    else:
        raise ValueError(f"Invalid product type: {type_}")

    # 执行查询
    result = db.session.execute(stmt).all()

    # 处理结果
    products_info = [{
        "id": product_id,
        "title": title,
        "title_image": title_image,
        "is_published": is_published,
        "has_info": has_info,
        "info_count": info_count
    } for product_id, title, title_image, is_published, has_info, info_count in result]

    return {
        'code': ErrorCode.SUCCESS.code,
        'data': products_info
    }


@bp.get('/categories/statistics')
@admin_required
def get_category_statistics():
    """管理员获取分类及其商品数量统计
    
    返回包含分类名和商品个数的列表，只统计未删除的商品
    """
    # 查询每个分类的未删除商品数量
    stmt = (
        select(
            Category.id,
            Category.name,
            func.count(ProductCategoryAssociation.product_id).label('product_count')
        )
        .outerjoin(
            ProductCategoryAssociation,
            Category.id == ProductCategoryAssociation.category_id
        )
        .outerjoin(
            Product,
            and_(
                Product.id == ProductCategoryAssociation.product_id,
                Product.deleted_at.is_(None)  # 只统计未删除的商品
            )
        )
        .group_by(Category.id, Category.name)
        .order_by(Category.sort_order)
    )

    result = db.session.execute(stmt).all()

    # 处理结果
    categories_statistics = [{
        "id": category_id,
        "name": name,
        "product_count": product_count
    } for category_id, name, product_count in result]

    return {
        'code': ErrorCode.SUCCESS.code,
        'data': categories_statistics
    }


@bp.get('/list-with-sku-count')
@admin_required
def get_products_with_sku_count():
    """管理员获取所有未删除商品的基本信息和SKU数量
    
    返回商品ID、标题、主图、发布状态以及SKU数量（被删除的SKU不计入数量）
    """
    # 构建查询：获取未删除商品及其未删除SKU的数量
    stmt = (
        select(
            Product.id,
            Product.title,
            Product.title_image,
            Product.is_published,
            func.count(SKU.id).label('sku_count')
        )
        .outerjoin(SKU, and_(
            Product.id == SKU.product_id,
            SKU.deleted_at.is_(None)  # 只统计未删除的SKU
        ))
        .where(Product.deleted_at.is_(None))
        .group_by(Product.id, Product.title, Product.title_image, Product.is_published)
        .order_by(Product.is_published.desc(), Product.id)
    )

    # 执行查询
    result = db.session.execute(stmt).all()

    # 处理结果
    products_info = [{
        "id": product_id,
        "title": title,
        "title_image": title_image,
        "is_published": is_published,
        "sku_count": sku_count
    } for product_id, title, title_image, is_published, sku_count in result]

    return {
        'code': ErrorCode.SUCCESS.code,
        'data': products_info
    }


@bp.get('/list-by-category')
@admin_required
@validate_with(GetProductsByCategorySchema)
def get_products_by_category(category_id):
    """管理员根据分类ID获取商品列表
    
    返回商品ID、标题、主图、发布状态以及SKU数量（被删除的SKU不计入数量）
    """
    # 构建查询：获取指定分类下未删除商品及其未删除SKU的数量
    stmt = (
        select(
            Product.id,
            Product.title,
            Product.title_image,
            Product.is_published,
            func.count(SKU.id).label('sku_count')
        )
        .join(
            ProductCategoryAssociation,
            Product.id == ProductCategoryAssociation.product_id
        )
        .outerjoin(SKU, and_(
            Product.id == SKU.product_id,
            SKU.deleted_at.is_(None)  # 只统计未删除的SKU
        ))
        .where(
            ProductCategoryAssociation.category_id == category_id,
            Product.deleted_at.is_(None)
        )
        .group_by(Product.id, Product.title, Product.title_image, Product.is_published)
        .order_by(Product.is_published.desc(), Product.id)
    )

    # 执行查询
    result = db.session.execute(stmt).all()

    # 处理结果
    products_info = [{
        "id": product_id,
        "title": title,
        "title_image": title_image,
        "is_published": is_published,
        "sku_count": sku_count
    } for product_id, title, title_image, is_published, sku_count in result]

    return {
        'code': ErrorCode.SUCCESS.code,
        'data': products_info
    }


@bp.get('/groups/statistics')
@admin_required
def get_group_statistics():
    """管理员获取商品分组及其商品数量统计
    
    返回包含分组名和商品个数的列表，只统计未删除的商品
    """
    # 查询每个分组的未删除商品数量
    stmt = (
        select(
            ProductGroup.id,
            ProductGroup.name,
            ProductGroup.display_order,
            ProductGroup.is_active,
            func.count(ProductGroupAssociation.product_id).label('product_count')
        )
        .outerjoin(
            ProductGroupAssociation,
            ProductGroup.id == ProductGroupAssociation.group_id
        )
        .outerjoin(
            Product,
            and_(
                Product.id == ProductGroupAssociation.product_id,
                Product.deleted_at.is_(None)  # 只统计未删除的商品
            )
        )
        .group_by(ProductGroup.id, ProductGroup.name, ProductGroup.display_order, ProductGroup.is_active)
        .order_by(ProductGroup.display_order)
    )

    result = db.session.execute(stmt).all()

    # 处理结果
    groups_statistics = [{
        "id": group_id,
        "name": name,
        "display_order": display_order,
        "is_active": is_active,
        "product_count": product_count
    } for group_id, name, display_order, is_active, product_count in result]

    return {
        'code': ErrorCode.SUCCESS.code,
        'data': groups_statistics
    }


@bp.get('/list-by-group')
@admin_required
@validate_with(GetProductsByGroupSchema)
def get_products_by_group(group_id):
    """管理员根据分组ID获取商品列表
    
    返回商品ID、标题、主图、发布状态以及SKU数量（被删除的SKU不计入数量）
    """
    # 构建查询：获取指定分组下未删除商品及其未删除SKU的数量
    stmt = (
        select(
            Product.id,
            Product.title,
            Product.title_image,
            Product.is_published,
            func.count(SKU.id).label('sku_count')
        )
        .join(
            ProductGroupAssociation,
            Product.id == ProductGroupAssociation.product_id
        )
        .outerjoin(SKU, and_(
            Product.id == SKU.product_id,
            SKU.deleted_at.is_(None)  # 只统计未删除的SKU
        ))
        .where(
            ProductGroupAssociation.group_id == group_id,
            Product.deleted_at.is_(None)
        )
        .group_by(Product.id, Product.title, Product.title_image, Product.is_published)
        .order_by(Product.is_published.desc(), Product.id)
    )

    # 执行查询
    result = db.session.execute(stmt).all()

    # 处理结果
    products_info = [{
        "id": product_id,
        "title": title,
        "title_image": title_image,
        "is_published": is_published,
        "sku_count": sku_count
    } for product_id, title, title_image, is_published, sku_count in result]

    return {
        'code': ErrorCode.SUCCESS.code,
        'data': products_info
    }


@bp.get('/channels/statistics')
@admin_required
def get_channel_statistics():
    """管理员获取频道及其商品数量统计
    
    返回包含频道名和商品个数的列表，只统计未删除的商品
    """
    # 查询每个频道的未删除商品数量
    stmt = (
        select(
            Channel.id,
            Channel.title,
            Channel.cover_url,
            Channel.display_order,
            Channel.is_active,
            func.count(ChannelProductAssociation.product_id).label('product_count')
        )
        .outerjoin(
            ChannelProductAssociation,
            Channel.id == ChannelProductAssociation.channel_id
        )
        .outerjoin(
            Product,
            and_(
                Product.id == ChannelProductAssociation.product_id,
                Product.deleted_at.is_(None)  # 只统计未删除的商品
            )
        )
        .group_by(Channel.id, Channel.title, Channel.cover_url, Channel.display_order, Channel.is_active)
        .order_by(Channel.display_order)
    )

    result = db.session.execute(stmt).all()

    # 处理结果
    channels_statistics = [{
        "id": channel_id,
        "name": title,
        "cover_url": cover_url,
        "display_order": display_order,
        "is_active": is_active,
        "product_count": product_count
    } for channel_id, title, cover_url, display_order, is_active, product_count in result]

    return {
        'code': ErrorCode.SUCCESS.code,
        'data': channels_statistics
    }


@bp.get('/list-by-channel')
@admin_required
@validate_with(GetProductsByChannelSchema)
def get_products_by_channel(channel_id):
    """管理员根据频道ID获取商品列表
    
    返回商品ID、标题、主图、发布状态以及SKU数量（被删除的SKU不计入数量）
    """
    # 构建查询：获取指定频道下未删除商品及其未删除SKU的数量
    stmt = (
        select(
            Product.id,
            Product.title,
            Product.title_image,
            Product.is_published,
            func.count(SKU.id).label('sku_count')
        )
        .join(
            ChannelProductAssociation,
            Product.id == ChannelProductAssociation.product_id
        )
        .outerjoin(SKU, and_(
            Product.id == SKU.product_id,
            SKU.deleted_at.is_(None)  # 只统计未删除的SKU
        ))
        .where(
            ChannelProductAssociation.channel_id == channel_id,
            Product.deleted_at.is_(None)
        )
        .group_by(Product.id, Product.title, Product.title_image, Product.is_published)
        .order_by(Product.is_published.desc(), Product.id)
    )

    # 执行查询
    result = db.session.execute(stmt).all()

    # 处理结果
    products_info = [{
        "id": product_id,
        "title": title,
        "title_image": title_image,
        "is_published": is_published,
        "sku_count": sku_count
    } for product_id, title, title_image, is_published, sku_count in result]

    return {
        'code': ErrorCode.SUCCESS.code,
        'data': products_info
    }


@bp.get('/regions/statistics')
@admin_required
def get_region_statistics():
    """管理员获取地域及其商品数量统计
    
    返回包含地域名和商品个数的列表，只统计未删除的商品
    """
    # 查询每个地域的未删除商品数量
    stmt = (
        select(
            Region.id,
            Region.title,
            Region.cover_url,
            Region.display_order,
            Region.display_type,
            Region.is_active,
            func.count(RegionProductAssociation.product_id).label('product_count')
        )
        .outerjoin(
            RegionProductAssociation,
            Region.id == RegionProductAssociation.region_id
        )
        .outerjoin(
            Product,
            and_(
                Product.id == RegionProductAssociation.product_id,
                Product.deleted_at.is_(None)  # 只统计未删除的商品
            )
        )
        .group_by(
            Region.id,
            Region.title,
            Region.cover_url,
            Region.display_order,
            Region.display_type,
            Region.is_active
        )
        .order_by(Region.display_order)
    )

    result = db.session.execute(stmt).all()

    # 处理结果
    regions_statistics = [{
        "id": region_id,
        "name": title,
        "cover_url": cover_url,
        "display_order": display_order,
        "display_type": display_type,
        "is_active": is_active,
        "product_count": product_count
    } for region_id, title, cover_url, display_order, display_type, is_active, product_count in result]

    return {
        'code': ErrorCode.SUCCESS.code,
        'data': regions_statistics
    }


@bp.get('/list-by-region')
@admin_required
@validate_with(GetProductsByRegionSchema)
def get_products_by_region(region_id):
    """管理员根据地域ID获取商品列表
    
    返回商品ID、标题、主图、发布状态以及SKU数量（被删除的SKU不计入数量）
    """
    # 构建查询：获取指定地域下未删除商品及其未删除SKU的数量
    stmt = (
        select(
            Product.id,
            Product.title,
            Product.title_image,
            Product.is_published,
            func.count(SKU.id).label('sku_count')
        )
        .join(
            RegionProductAssociation,
            Product.id == RegionProductAssociation.product_id
        )
        .outerjoin(SKU, and_(
            Product.id == SKU.product_id,
            SKU.deleted_at.is_(None)  # 只统计未删除的SKU
        ))
        .where(
            RegionProductAssociation.region_id == region_id,
            Product.deleted_at.is_(None)
        )
        .group_by(Product.id, Product.title, Product.title_image, Product.is_published)
        .order_by(Product.is_published.desc(), Product.id)
    )

    # 执行查询
    result = db.session.execute(stmt).all()

    # 处理结果
    products_info = [{
        "id": product_id,
        "title": title,
        "title_image": title_image,
        "is_published": is_published,
        "sku_count": sku_count
    } for product_id, title, title_image, is_published, sku_count in result]

    return {
        'code': ErrorCode.SUCCESS.code,
        'data': products_info
    }


@bp.put('/publish-status')
@admin_required
@validate_with(UpdateProductPublishStatusSchema)
def update_product_publish_status(product_id, is_published):
    """管理员更新商品上架/下架状态
    
    根据传入的商品ID和上架状态，更新商品的上架/下架状态
    如果商品当前状态已经是目标状态，则返回错误码1
    如果是上架商品，检查该商品是否有有效sku（未被软删除、上架中），如果没有，则返回错误码NO_VALID_SKU
    """
    # 创建并执行用例
    use_case = UpdateProductPublishStatusUseCase()

    # 执行用例并返回结果
    result = use_case.execute(product_id, is_published)

    return {
        'code': ErrorCode.SUCCESS.code,
        'data': result
    }


@bp.get('/selection-lists')
@admin_required
def get_selection_lists():
    """管理员获取产品展示组、地域和导航频道的选择列表
    
    返回三个列表：
    1. recommend: 产品展示组列表
    2. region: 地域列表
    3. navigation: 导航频道列表
    
    每个列表项包含 label 和 value 字段，如果未开启，添加 tag 字段
    每个列表额外添加一个"不添加该频道"选项，value 为 -1
    """
    # 获取产品展示组列表
    product_groups = ProductGroup.query.order_by(ProductGroup.display_order).all()
    recommend_list = [{
        'label': group.name,
        'value': group.id,
        **({'tag': '未开启'} if not group.is_active else {})
    } for group in product_groups]
    recommend_list.insert(0, {'label': '不添加该频道', 'value': -1})

    # 获取地域列表
    regions = Region.query.order_by(Region.display_order).all()
    region_list = [{
        'label': region.title,
        'value': region.id,
        **({'tag': '未开启'} if not region.is_active else {})
    } for region in regions]
    region_list.insert(0, {'label': '不添加该频道', 'value': -1})

    # 获取导航频道列表
    channels = Channel.query.order_by(Channel.display_order).all()
    navigation_list = [{
        'label': channel.title,
        'value': channel.id,
        **({'tag': '未开启'} if not channel.is_active else {})
    } for channel in channels]
    navigation_list.insert(0, {'label': '不添加该频道', 'value': -1})

    return {
        'code': ErrorCode.SUCCESS.code,
        'data': {
            'recommend': recommend_list,
            'region': region_list,
            'navigation': navigation_list
        }
    }
