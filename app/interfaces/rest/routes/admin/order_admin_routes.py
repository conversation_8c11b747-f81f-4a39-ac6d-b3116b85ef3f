from datetime import datetime, time

from flask import Blueprint, g, current_app

from app.application.order.use_cases.admin_after_sale_pending_detail import AdminAfterSalePendingDetailUseCase
from app.application.order.use_cases.admin_after_sale_pending_orders import AdminAfterSalePendingOrdersUseCase
from app.application.order.use_cases.admin_ship_order import AdminShipOrderUseCase
from app.application.order.use_cases.admin_sub_order_detail import AdminSubOrderDetailUseCase
from app.application.order.use_cases.process_after_sale import ProcessAfterSaleUseCase
from app.constants import ErrorCode
from app.decorators.auth import admin_required
from app.decorators.validation import validate_with
from app.domain.order.services import OrderService
from app.domain.order.value_objects import SubOrderStatus
from app.domain.product.services import ProductService
from app.extensions import rdb, db
from app.infrastructure.coupon.repositories import SQLUserCouponRepository, SQLCouponAllocationRepository
from app.infrastructure.order.logistics_subscription_factory import logistics_subscription_factory
from app.infrastructure.order.models import SubOrderModel, OrderModel
from app.infrastructure.order.repositories import SQLOrderRepository
from app.infrastructure.payment.factory import PaymentServiceFactory
from app.infrastructure.product.repositories import SQLProductRepository
from app.infrastructure.repositories import SQLTransactionManager
from app.infrastructure.user.repositories import SQLGroupLeaderCommissionRepository, SQLUserRepository, \
    SQLGroupMemberRepository
from app.interfaces.rest.routes.order_routes import create_order_repository
from app.interfaces.rest.schemas.order_schemas import AdminShipOrderSchema, AdminGetPaidOrdersSchema, \
    AdminQuerySubOrdersSchema, AdminSubOrderDetailSchema, AdminGetShippedOrdersSchema, RejectAfterSaleSchema, \
    RefundAfterSaleSchema, AdminGetAfterSalePendingOrdersSchema, AdminAfterSalePendingDetailSchema, \
    AdminGetCompletedOrdersSchema, AdminGetAllOrdersSchema, AdminGetTodayOrdersSchema

bp = Blueprint('admin_order', __name__, url_prefix='/api/admin/orders')


@bp.get('/paid')
@admin_required
@validate_with(AdminGetPaidOrdersSchema)
def admin_get_paid_orders(page, per_page):
    """获取所有已付款订单

    获取所有状态为已付款（待发货）的子订单，带分页，返回子订单号、状态、商品信息等
    """

    repo = SQLOrderRepository()
    product_service = ProductService(SQLProductRepository())

    # 获取待发货子订单分页数据
    sub_orders, total = repo.find_paginated_sub_orders_by_status(
        status=SubOrderStatus.PAID,
        page=page,
        per_page=per_page
    )

    items = []
    for so in sub_orders:
        # 获取SKU和商品信息
        sku_info = product_service.get_product_sku_info(so.sku_id)

        # 构建子订单数据
        items.append({
            "order_no": so.order_no,
            "status": so.status.value,
            "product_title": sku_info["title"],
            "sku_image": sku_info["sku"]["image"] or sku_info["title_image"],
            "sku_display_title": sku_info["sku"]["display_title"],
            "quantity": so.quantity
        })

    return {
        "code": ErrorCode.SUCCESS.code,
        "data": {
            "items": items,
            "pagination": {
                "page": page,
                "per_page": per_page,
                "total": total,
                "total_pages": (total + per_page - 1) // per_page
            }
        }
    }


@bp.get('/pending-shipment-format')
@admin_required
def admin_get_pending_shipment_format():
    """获取待发货订单格式化数据

    获取所有状态为已付款（待发货）的子订单，不分页，返回格式化的待发货信息列表
    """
    product_service = ProductService(SQLProductRepository())

    # 直接使用SQL查询所有待发货子订单
    stmt = db.select(SubOrderModel).where(
        SubOrderModel.status == SubOrderStatus.PAID.value,
        SubOrderModel.deleted_at.is_(None)
    ).order_by(SubOrderModel.id.desc())

    # 执行查询
    result = db.session.execute(stmt)
    sub_order_models = result.scalars().all()

    # 收集所有子订单的父订单ID
    parent_order_ids = [so.order_id for so in sub_order_models]

    # 直接查询父订单信息
    parent_orders = {}
    if parent_order_ids:
        # 使用SQL查询父订单
        parent_stmt = db.select(OrderModel).where(
            OrderModel.id.in_(parent_order_ids),
            OrderModel.deleted_at.is_(None)
        )
        parent_result = db.session.execute(parent_stmt)
        parent_order_models = parent_result.scalars().all()

        # 将父订单按ID索引
        parent_orders = {order.id: order for order in parent_order_models}

    items = []
    for index, so in enumerate(sub_order_models, 1):
        # 获取SKU和商品信息
        sku_info = product_service.get_product_sku_info(so.sku_id)

        # 获取父订单信息（包含收货地址）
        parent_order = parent_orders.get(so.order_id)

        # 构建收货地址信息
        address_full = ""
        receiver_name = ""
        receiver_phone = ""
        order_time = ""

        if parent_order and parent_order.shipping_address:
            # 解析JSON格式的收货地址
            shipping_address = parent_order.shipping_address
            province = shipping_address.get('province', '')
            city = shipping_address.get('city', '')
            district = shipping_address.get('district', '')
            detail = shipping_address.get('detail', '')
            address_full = f"{province}{city}{district}{detail}"
            receiver_name = shipping_address.get('contact_name', '')
            receiver_phone = shipping_address.get('contact_phone', '')

        # 获取下单时间
        if parent_order:
            order_time = parent_order.created_at.strftime("%Y-%m-%d %H:%M:%S") if parent_order.created_at else ""

        # 实付金额
        actual_amount = str(so.actual_payment) if so.actual_payment else "0.00"

        # 构建子订单数据
        items.append({
            "serial_number": str(index),  # 序号
            "order_number": so.order_no,  # 订单号
            "order_time": order_time,  # 下单时间
            "product_name": sku_info["title"],  # 商品名称
            "sku": sku_info["sku"]["display_title"],  # SKU
            "actual_amount": actual_amount,  # 实付金额
            "receiver_name": receiver_name,  # 收货人
            "receiver_phone": receiver_phone,  # 联系电话
            "receiver_address": address_full,  # 收货地址
        })

    return {
        "code": ErrorCode.SUCCESS.code,
        "data": {
            "items": items
        }
    }


@bp.post('/ship')
@admin_required
@validate_with(AdminShipOrderSchema)
def admin_ship_order(order_no, logistics_company, tracking_number):
    """管理员发货接口

    接收子订单号、物流公司编码和快递单号，执行发货流程
    """
    # 创建用例
    use_case = AdminShipOrderUseCase(
        order_service=OrderService(
            redis_client=rdb.client,
            logger=current_app.logger,
            order_repository=SQLOrderRepository(),
            logistics_subscription_service=logistics_subscription_factory.service
        ),
        order_repo=SQLOrderRepository(),
        db_ctx=SQLTransactionManager()
    )

    # 清理快递单号，去除前后空白字符和换行符
    tracking_number = tracking_number.strip() if tracking_number else ""

    # 执行用例
    result = use_case.execute(
        order_no=order_no,
        logistics_company=logistics_company,
        tracking_number=tracking_number,
        operator_id=g.auth['admin_user_id']
    )

    return {
        "code": ErrorCode.SUCCESS.code,
        "data": result
    }


@bp.get('/query')
@admin_required
@validate_with(AdminQuerySubOrdersSchema)
def admin_query_sub_orders(order_query, status, page, per_page):
    """管理员查询子订单接口

    根据主订单号后四位或完整子订单号和指定状态查询子订单，带分页，返回子订单号、状态、商品信息等
    当状态为ship时，将聚合查询已发货、已签收、已拒签的子订单
    当状态为all时，将查询所有子订单
    当状态为today时，将查询今日创建的子订单
    """
    repo = SQLOrderRepository()
    product_service = ProductService(SQLProductRepository())

    # 处理特殊的查询条件
    if status == "all":
        # 查询所有子订单
        # 优化查询逻辑：先判断长度，再进行相应查询
        if len(order_query) == 4:
            # 主订单号后四位查询
            # 构建子订单号后缀匹配条件：主订单号后四位-数字
            stmt = db.select(SubOrderModel).where(
                SubOrderModel.deleted_at.is_(None),
                db.func.substring(SubOrderModel.order_no, -6, 4) == order_query
            ).order_by(SubOrderModel.id.desc())

            # 计算总数
            count_stmt = db.select(db.func.count()).select_from(SubOrderModel).where(
                SubOrderModel.deleted_at.is_(None),
                db.func.substring(SubOrderModel.order_no, -6, 4) == order_query
            )
            total = db.session.scalar(count_stmt)

            # 执行分页查询
            result = db.session.execute(
                stmt.offset((page - 1) * per_page).limit(per_page)
            )
            sub_orders = result.scalars().all()
        else:
            # 完整子订单号查询
            stmt = db.select(SubOrderModel).where(
                SubOrderModel.deleted_at.is_(None),
                SubOrderModel.order_no == order_query
            ).order_by(SubOrderModel.id.desc())

            # 计算总数
            count_stmt = db.select(db.func.count()).select_from(SubOrderModel).where(
                SubOrderModel.deleted_at.is_(None),
                SubOrderModel.order_no == order_query
            )
            total = db.session.scalar(count_stmt)

            # 执行查询
            result = db.session.execute(stmt)
            sub_orders = result.scalars().all()

    elif status == "today":
        # 查询今日创建的子订单
        # 获取今天的开始和结束时间
        today = datetime.now().date()
        today_start = datetime.combine(today, time.min)
        today_end = datetime.combine(today, time.max)

        # 根据查询条件长度判断查询方式
        if len(order_query) == 4:
            # 主订单号后四位查询
            stmt = db.select(SubOrderModel).where(
                SubOrderModel.created_at.between(today_start, today_end),
                SubOrderModel.deleted_at.is_(None),
                db.func.substring(SubOrderModel.order_no, -6, 4) == order_query
            ).order_by(SubOrderModel.id.desc())

            # 计算总数
            count_stmt = db.select(db.func.count()).select_from(SubOrderModel).where(
                SubOrderModel.created_at.between(today_start, today_end),
                SubOrderModel.deleted_at.is_(None),
                db.func.substring(SubOrderModel.order_no, -6, 4) == order_query
            )
            total = db.session.scalar(count_stmt)

            # 执行分页查询
            result = db.session.execute(
                stmt.offset((page - 1) * per_page).limit(per_page)
            )
            sub_orders = result.scalars().all()
        else:
            # 完整子订单号查询
            stmt = db.select(SubOrderModel).where(
                SubOrderModel.created_at.between(today_start, today_end),
                SubOrderModel.deleted_at.is_(None),
                SubOrderModel.order_no == order_query
            ).order_by(SubOrderModel.id.desc())

            # 计算总数
            count_stmt = db.select(db.func.count()).select_from(SubOrderModel).where(
                SubOrderModel.created_at.between(today_start, today_end),
                SubOrderModel.deleted_at.is_(None),
                SubOrderModel.order_no == order_query
            )
            total = db.session.scalar(count_stmt)

            # 执行查询
            result = db.session.execute(stmt)
            sub_orders = result.scalars().all()
    elif status == "ship":
        # 处理特殊的"ship"状态聚合查询
        # 定义目标状态列表
        target_statuses = [
            SubOrderStatus.SHIPPED,  # 已发货
            SubOrderStatus.DELIVERED,  # 已签收
            SubOrderStatus.REJECTED  # 已拒签
        ]

        # 查询多个状态的子订单
        sub_orders, total = repo.find_paginated_sub_orders_by_query_and_multiple_statuses(
            order_query=order_query,
            statuses=target_statuses,
            page=page,
            per_page=per_page
        )
    else:
        # 将字符串状态转换为枚举值
        status_enum = SubOrderStatus(status)

        # 查询子订单
        sub_orders, total = repo.find_paginated_sub_orders_by_query_and_status(
            order_query=order_query,
            status=status_enum,
            page=page,
            per_page=per_page
        )

    # 获取所有子订单对应的主订单信息，用于提取收货地址
    # 首先，收集所有子订单的主订单ID
    parent_order_ids = []
    sub_order_models = []

    # 区分领域实体和数据模型
    if isinstance(sub_orders, list) and sub_orders and hasattr(sub_orders[0], 'order_id'):
        # 领域实体
        parent_order_ids = [so.order_id for so in sub_orders]
        sub_order_models = sub_orders
    else:
        # 数据模型
        parent_order_ids = [so.order_id for so in sub_orders]
        sub_order_models = sub_orders

    # 查询所有相关的主订单
    parent_orders = {}
    if parent_order_ids:
        # 查询这些主订单的信息
        order_results = db.session.execute(
            db.select(OrderModel).where(
                OrderModel.id.in_(parent_order_ids),
                OrderModel.deleted_at.is_(None)
            )
        ).scalars().all()

        # 构建一个字典，以订单ID为键
        parent_orders = {order.id: order for order in order_results}

    items = []
    for so in sub_order_models:
        # 获取SKU和商品信息
        sku_info = product_service.get_product_sku_info(so.sku_id)

        # 获取父订单信息
        parent_order = parent_orders.get(so.order_id)

        # 准备地址和联系人信息
        address_full = ""
        contact_info = ""

        if parent_order and parent_order.shipping_address:
            # 解析父订单的收货地址信息
            shipping_address = parent_order.shipping_address

            # 构建完整地址
            if isinstance(shipping_address, dict):
                province = shipping_address.get('province', '')
                city = shipping_address.get('city', '')
                district = shipping_address.get('district', '')
                detail = shipping_address.get('detail', '')
                address_full = f"{province}{city}{district}{detail}"

                # 构建联系人信息
                contact_name = shipping_address.get('contact_name', '')
                contact_phone = shipping_address.get('contact_phone', '')
                contact_info = f"{contact_name} {contact_phone}"

        # 构建子订单数据
        items.append({
            "order_no": so.order_no,
            "status": so.status.value if isinstance(so.status, SubOrderStatus) else so.status,
            "product_title": sku_info["title"],
            "sku_image": sku_info["sku"]["image"] or sku_info["title_image"],
            "sku_display_title": sku_info["sku"]["display_title"],
            "quantity": so.quantity,
            "address_full": address_full,  # 添加完整地址
            "contact_info": contact_info  # 添加联系人信息
        })

    return {
        "code": ErrorCode.SUCCESS.code,
        "data": {
            "items": items,
            "pagination": {
                "page": page,
                "per_page": per_page,
                "total": total,
                "total_pages": (total + per_page - 1) // per_page
            }
        }
    }


@bp.get('/detail')
@admin_required
@validate_with(AdminSubOrderDetailSchema)
def admin_get_sub_order_detail(order_no):
    """管理员获取子订单详情接口

    接收子订单号，返回子订单详细信息，包括订单状态、商品信息、收货地址、支付信息、物流信息等
    """
    # 创建用例
    use_case = AdminSubOrderDetailUseCase(
        order_repo=SQLOrderRepository(),
        product_service=ProductService(SQLProductRepository()),
        coupon_allocation_repo=SQLCouponAllocationRepository(),
        user_coupon_repo=SQLUserCouponRepository(),
        user_repo=SQLUserRepository(),
        group_member_repo=SQLGroupMemberRepository(),
        commission_repo=SQLGroupLeaderCommissionRepository(),
        redis_client=rdb.client,
        logger=current_app.logger
    )

    # 执行用例
    result = use_case.execute(order_no=order_no)

    return {
        "code": ErrorCode.SUCCESS.code,
        "data": result
    }


@bp.get('/ship')
@admin_required
@validate_with(AdminGetShippedOrdersSchema)
def admin_get_shipped_orders(page, per_page):
    """获取所有已发货订单

    获取所有状态为已发货、已签收和已拒签的子订单，带分页，返回子订单号、状态、商品信息等
    """
    repo = SQLOrderRepository()
    product_service = ProductService(SQLProductRepository())

    # 定义目标状态列表
    target_statuses = [
        SubOrderStatus.SHIPPED,  # 已发货
        SubOrderStatus.DELIVERED,  # 已签收
        SubOrderStatus.REJECTED  # 已拒签
    ]

    # 使用多状态查询方法，直接在数据库层面进行分页
    sub_orders, total = repo.find_paginated_sub_orders_by_multiple_statuses(
        statuses=target_statuses,
        page=page,
        per_page=per_page
    )

    # 构建返回数据
    items = []
    for so in sub_orders:
        # 获取SKU和商品信息
        sku_info = product_service.get_product_sku_info(so.sku_id)

        # 构建子订单数据
        items.append({
            "order_no": so.order_no,
            "status": so.status.value,
            "product_title": sku_info["title"],
            "sku_image": sku_info["sku"]["image"] or sku_info["title_image"],
            "sku_display_title": sku_info["sku"]["display_title"],
            "quantity": so.quantity
            # TODO: 加联系人信息
        })

    return {
        "code": ErrorCode.SUCCESS.code,
        "data": {
            "items": items,
            "pagination": {
                "page": page,
                "per_page": per_page,
                "total": total,
                "total_pages": (total + per_page - 1) // per_page
            }
        }
    }


@bp.post('/after-sale/reject')
@admin_required
@validate_with(RejectAfterSaleSchema)
def reject_after_sale(order_no, description):
    """拒绝售后（管理员）

    管理员拒绝用户的售后申请，需要提供拒绝说明
    """
    # 创建用例
    use_case = ProcessAfterSaleUseCase(
        order_repo=create_order_repository(),
        order_service=OrderService(
            redis_client=rdb.client,
            logger=current_app.logger,
            order_repository=SQLOrderRepository()
        ),
        db_ctx=SQLTransactionManager(),
        payment_service=PaymentServiceFactory.create_payment_service(),
        logger=current_app.logger
    )

    # 执行用例
    result = use_case.reject(
        admin_id=g.auth['admin_user_id'],
        order_no=order_no,
        description=description
    )

    return {
        "code": ErrorCode.SUCCESS.code,
        "data": result
    }


@bp.post('/after-sale/refund')
@admin_required
@validate_with(RefundAfterSaleSchema)
def refund_after_sale(order_no, description, refund_amount):
    """售后退款（管理员）

    管理员处理用户的售后申请，执行退款操作，可以选择全额或部分退款
    """
    # 创建用例
    use_case = ProcessAfterSaleUseCase(
        order_repo=create_order_repository(),
        order_service=OrderService(
            redis_client=rdb.client,
            logger=current_app.logger,
            order_repository=SQLOrderRepository(),
            payment_service=PaymentServiceFactory.create_payment_service()
        ),
        db_ctx=SQLTransactionManager(),
        payment_service=PaymentServiceFactory.create_payment_service(),
        logger=current_app.logger
    )

    # 执行用例
    result = use_case.refund(
        admin_id=g.auth['admin_user_id'],
        order_no=order_no,
        description=description,
        refund_amount=refund_amount
    )

    return {
        "code": ErrorCode.SUCCESS.code,
        "data": result
    }


@bp.get('/after-sale/pending')
@admin_required
@validate_with(AdminGetAfterSalePendingOrdersSchema)
def admin_get_after_sale_pending_orders(page, per_page):
    """获取所有待售后订单（管理员）

    获取所有状态为待售后处理的子订单，带分页，返回子订单号、状态、商品信息等
    """
    # 创建用例
    use_case = AdminAfterSalePendingOrdersUseCase(
        order_repo=SQLOrderRepository(),
        product_service=ProductService(SQLProductRepository())
    )

    # 执行用例
    result = use_case.execute(page=page, per_page=per_page)

    return {
        "code": ErrorCode.SUCCESS.code,
        "data": result
    }


@bp.get('/after-sale/detail')
@admin_required
@validate_with(AdminAfterSalePendingDetailSchema)
def admin_get_after_sale_pending_detail(order_no):
    """获取待售后订单详情（管理员）

    获取待售后处理子订单的详细信息，包括订单状态、商品信息、收货地址、支付信息、物流信息、售后申请信息等
    """
    # 创建用例
    use_case = AdminAfterSalePendingDetailUseCase(
        order_repo=SQLOrderRepository(),
        product_service=ProductService(SQLProductRepository()),
        coupon_repo=SQLCouponAllocationRepository(),
        logger=current_app.logger
    )

    # 执行用例
    result = use_case.execute(order_no=order_no)

    return {
        "code": ErrorCode.SUCCESS.code,
        "data": result
    }


@bp.get('/completed')
@admin_required
@validate_with(AdminGetCompletedOrdersSchema)
def admin_get_completed_orders(page, per_page):
    """获取所有已完成订单

    获取所有状态为已完成的子订单，带分页，返回子订单号、状态、商品信息等
    """
    product_service = ProductService(SQLProductRepository())

    # 使用SQLAlchemy 2.x语法直接查询已完成子订单
    stmt = db.select(SubOrderModel).where(
        SubOrderModel.status == "completed",
        SubOrderModel.deleted_at.is_(None)
    ).order_by(SubOrderModel.id.desc())

    # 计算总数
    count_stmt = db.select(db.func.count()).select_from(SubOrderModel).where(
        SubOrderModel.status == "completed",
        SubOrderModel.deleted_at.is_(None)
    )
    total = db.session.scalar(count_stmt)

    # 执行分页查询
    result = db.session.execute(
        stmt.offset((page - 1) * per_page).limit(per_page)
    )
    sub_orders = result.scalars().all()

    # 构建返回数据
    items = []
    for so in sub_orders:
        # 获取SKU和商品信息
        sku_info = product_service.get_product_sku_info(so.sku_id)

        # 构建子订单数据
        items.append({
            "order_no": so.order_no,
            "status": so.status,
            "product_title": sku_info["title"],
            "sku_image": sku_info["sku"]["image"] or sku_info["title_image"],
            "sku_display_title": sku_info["sku"]["display_title"],
            "quantity": so.quantity,
            "completed_at": so.completed_at.strftime("%Y-%m-%d %H:%M:%S") if so.completed_at else None
        })

    return {
        "code": ErrorCode.SUCCESS.code,
        "data": {
            "items": items,
            "pagination": {
                "page": page,
                "per_page": per_page,
                "total": total,
                "total_pages": (total + per_page - 1) // per_page
            }
        }
    }


@bp.get('/all')
@admin_required
@validate_with(AdminGetAllOrdersSchema)
def admin_get_all_orders(page, per_page):
    """获取所有子订单

    获取所有子订单，带分页，返回子订单号、状态、商品信息等
    """
    product_service = ProductService(SQLProductRepository())

    # 使用SQLAlchemy 2.x语法直接查询所有子订单
    stmt = db.select(SubOrderModel).where(
        SubOrderModel.deleted_at.is_(None)
    ).order_by(SubOrderModel.id.desc())

    # 计算总数
    count_stmt = db.select(db.func.count()).select_from(SubOrderModel).where(
        SubOrderModel.deleted_at.is_(None)
    )
    total = db.session.scalar(count_stmt)

    # 执行分页查询
    result = db.session.execute(
        stmt.offset((page - 1) * per_page).limit(per_page)
    )
    sub_orders = result.scalars().all()

    # 构建返回数据
    items = []
    for so in sub_orders:
        # 获取SKU和商品信息
        sku_info = product_service.get_product_sku_info(so.sku_id)

        # 构建子订单数据
        items.append({
            "order_no": so.order_no,
            "status": so.status,
            "product_title": sku_info["title"],
            "sku_image": sku_info["sku"]["image"] or sku_info["title_image"],
            "sku_display_title": sku_info["sku"]["display_title"],
            "quantity": so.quantity,
            "created_at": so.created_at.strftime("%Y-%m-%d %H:%M:%S")
        })

    return {
        "code": ErrorCode.SUCCESS.code,
        "data": {
            "items": items,
            "pagination": {
                "page": page,
                "per_page": per_page,
                "total": total,
                "total_pages": (total + per_page - 1) // per_page
            }
        }
    }


@bp.get('/today')
@admin_required
@validate_with(AdminGetTodayOrdersSchema)
def admin_get_today_orders(page, per_page):
    """获取今日子订单

    获取今日创建的所有子订单，带分页，返回子订单号、状态、商品信息等
    """
    product_service = ProductService(SQLProductRepository())

    # 获取今天的开始和结束时间
    today = datetime.now().date()
    today_start = datetime.combine(today, time.min)
    today_end = datetime.combine(today, time.max)

    # 使用SQLAlchemy 2.x语法直接查询今日子订单
    stmt = db.select(SubOrderModel).where(
        SubOrderModel.created_at.between(today_start, today_end),
        SubOrderModel.deleted_at.is_(None)
    ).order_by(SubOrderModel.id.desc())

    # 计算总数
    count_stmt = db.select(db.func.count()).select_from(SubOrderModel).where(
        SubOrderModel.created_at.between(today_start, today_end),
        SubOrderModel.deleted_at.is_(None)
    )
    total = db.session.scalar(count_stmt)

    # 执行分页查询
    result = db.session.execute(
        stmt.offset((page - 1) * per_page).limit(per_page)
    )
    sub_orders = result.scalars().all()

    # 构建返回数据
    items = []
    for so in sub_orders:
        # 获取SKU和商品信息
        sku_info = product_service.get_product_sku_info(so.sku_id)

        # 构建子订单数据
        items.append({
            "order_no": so.order_no,
            "status": so.status,
            "product_title": sku_info["title"],
            "sku_image": sku_info["sku"]["image"] or sku_info["title_image"],
            "sku_display_title": sku_info["sku"]["display_title"],
            "quantity": so.quantity,
            "created_at": so.created_at.strftime("%Y-%m-%d %H:%M:%S")
        })

    return {
        "code": ErrorCode.SUCCESS.code,
        "data": {
            "items": items,
            "pagination": {
                "page": page,
                "per_page": per_page,
                "total": total,
                "total_pages": (total + per_page - 1) // per_page
            }
        }
    }


@bp.get('/compare-unshipped')
@admin_required
def admin_compare_unshipped_orders():
    """管理员比较未发货订单接口

    获取所有当前未发货的子订单的母订单的微信支付id集合，
    获取所有微信支付平台的未发货订单列表的微信支付id集合，
    然后对比，返回是否一致的Boolean。
    """
    from app.application.order.use_cases.compare_unshipped_orders_use_case import CompareUnshippedOrdersUseCase
    from app.infrastructure.payment.services import WechatPaymentService

    # 创建用例
    use_case = CompareUnshippedOrdersUseCase(
        order_repo=SQLOrderRepository(),
        payment_service=WechatPaymentService()
    )

    # 执行用例
    result = use_case.execute()

    return {
        "code": ErrorCode.SUCCESS.code,
        "data": result
    }
